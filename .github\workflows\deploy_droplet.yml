name: 🚀 Deploy to Droplet

on:
  repository_dispatch:
    types: [deploy-staging, deploy-production]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
          - staging
          - production
        default: staging
      image_tag:
        description: 'Docker image tag to deploy'
        required: false
        type: string
        default: 'latest'
      reason:
        description: 'Reason for this manual deployment'
        required: true
        type: string

jobs:
  deploy-staging:
    runs-on: ubuntu-latest
    environment: staging
    if: |
      (github.event_name == 'repository_dispatch' && github.event.action == 'deploy-staging') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set deployment variables for staging
        id: vars
        run: |
          # Handle both repository_dispatch and workflow_dispatch triggers
          if [[ "${{ github.event_name }}" == "repository_dispatch" ]]; then
            IMAGE_TAG="${{ github.event.client_payload.image_tag }}"
            COMMIT_SHA="${{ github.event.client_payload.commit_sha }}"
            BUILD_NUMBER="${{ github.event.client_payload.build_number }}"
            DEPLOYED_BY="${{ github.event.client_payload.deployed_by }}"
            REASON="${{ github.event.client_payload.reason }}"
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            IMAGE_TAG="${{ github.event.inputs.image_tag }}"
            COMMIT_SHA="${{ github.sha }}"
            BUILD_NUMBER="${{ github.run_number }}"
            DEPLOYED_BY="${{ github.actor }}"
            REASON="${{ github.event.inputs.reason }}"
          else
            echo "❌ Unsupported event type: ${{ github.event_name }}"
            exit 1
          fi

          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV
          echo "COMMIT_SHA=${COMMIT_SHA}" >> $GITHUB_ENV
          echo "BUILD_NUMBER=${BUILD_NUMBER}" >> $GITHUB_ENV
          echo "DEPLOYED_BY=${DEPLOYED_BY}" >> $GITHUB_ENV
          echo "REASON=${REASON}" >> $GITHUB_ENV

          # Set staging deployment paths and compose files
          echo "DEPLOY_PATH=/opt/yezhome/yezhome-staging" >> $GITHUB_ENV
          echo "COMPOSE_FILE=docker-compose.staging.yml" >> $GITHUB_ENV
          echo "ENV_FILE_LOCAL=.env.staging" >> $GITHUB_ENV
          echo "ENV_FILE_REMOTE=.env" >> $GITHUB_ENV

      - name: Create environment file for staging
        run: |
          echo "📝 Creating staging environment file..."

          ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
          echo "# Deployment metadata" > ${ENV_FILE}
          echo "IMAGE_TAG=${{ env.IMAGE_TAG }}" >> ${ENV_FILE}
          echo "BUILD_NUMBER=${{ env.BUILD_NUMBER }}" >> ${ENV_FILE}
          echo "COMMIT_SHA=${{ env.COMMIT_SHA }}" >> ${ENV_FILE}
          echo "LAST_DEPLOYED=$(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# Database Configuration - Staging" >> ${ENV_FILE}
          echo "DB_HOST=${{ vars.DB_HOST }}" >> ${ENV_FILE}
          echo "POSTGRES_DB=${{ vars.POSTGRES_DB }}" >> ${ENV_FILE}
          echo "POSTGRES_USER=${{ secrets.DB_USER }}" >> ${ENV_FILE}
          echo "POSTGRES_PASSWORD=${{ secrets.DB_PASSWORD }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# JWT Configuration - Staging" >> ${ENV_FILE}
          echo "JWT_KEY=${{ secrets.JWT_KEY }}" >> ${ENV_FILE}
          echo "JWT_ISSUER=${{ vars.JWT_ISSUER }}" >> ${ENV_FILE}
          echo "JWT_AUDIENCE=${{ vars.JWT_AUDIENCE }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# AWS Configuration - Staging" >> ${ENV_FILE}
          echo "AWS_S3_ACCESS_KEY=${{ secrets.AWS_S3_ACCESS_KEY }}" >> ${ENV_FILE}
          echo "AWS_S3_SECRET_KEY=${{ secrets.AWS_S3_SECRET_KEY }}" >> ${ENV_FILE}
          echo "AWS_S3_REGION=${{ vars.AWS_S3_REGION }}" >> ${ENV_FILE}
          echo "AWS_S3_BUCKET_NAME=${{ vars.AWS_S3_BUCKET_NAME }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# AWS SES Configuration - Staging" >> ${ENV_FILE}
          echo "AWS_SES_ACCESS_KEY=${{ secrets.AWS_SES_ACCESS_KEY }}" >> ${ENV_FILE}
          echo "AWS_SES_SECRET_KEY=${{ secrets.AWS_SES_SECRET_KEY }}" >> ${ENV_FILE}
          echo "AWS_SES_REGION=${{ vars.AWS_SES_REGION }}" >> ${ENV_FILE}
          echo "AWS_SES_FROM_EMAIL=${{ vars.AWS_SES_FROM_EMAIL }}" >> ${ENV_FILE}
          echo "AWS_SES_FROM_NAME=${{ vars.AWS_SES_FROM_NAME }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# Internal API Configuration - Staging" >> ${ENV_FILE}
          echo "INTERNAL_JWT_KEY=${{ secrets.INTERNAL_JWT_KEY }}" >> ${ENV_FILE}
          echo "INTERNAL_JWT_ISSUER=${{ vars.INTERNAL_JWT_ISSUER }}" >> ${ENV_FILE}
          echo "INTERNAL_JWT_AUDIENCE=${{ vars.INTERNAL_JWT_AUDIENCE }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          # Add deployment metadata
          echo "DEPLOYED_BY=${{ env.DEPLOYED_BY }}" >> ${ENV_FILE}

      - name: Create deployment directory on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            echo "📁 Creating deployment directory: ${{ env.DEPLOY_PATH }}"
            mkdir -p ${{ env.DEPLOY_PATH }}
            echo "✅ Directories created successfully"

      - name: Copy files to Droplet
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          source: "Dockerfile,${{ env.COMPOSE_FILE }},${{ env.ENV_FILE_LOCAL }}"
          target: ${{ env.DEPLOY_PATH }}
          
      - name: Rename environment file on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            cd ${{ env.DEPLOY_PATH }}
            mv ${{ env.ENV_FILE_LOCAL }} ${{ env.ENV_FILE_REMOTE }}
            echo "✅ Environment file renamed"

      - name: SSH to Droplet and deploy staging
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 600s
          command_timeout: 15m
          script: |
            echo "🚀 Deploying to staging environment..."
            echo "📁 Deploy path: ${{ env.DEPLOY_PATH }}"
            echo "🐳 Image tag: ${{ env.IMAGE_TAG }}"
            echo "📄 Compose file: ${{ env.COMPOSE_FILE }}"

            # Navigate to deployment directory
            cd ${{ env.DEPLOY_PATH }} || { echo "❌ Failed to change directory"; exit 1; }

            # Set secure permissions
            chmod 600 ${{ env.ENV_FILE_REMOTE }}

            echo "🔐 Logging into GitHub Container Registry..."
            echo "${{ secrets.PULL_PAT }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            echo "🔄 Pulling latest images..."
            docker compose -f ${{ env.COMPOSE_FILE }} pull || { echo "❌ Failed to pull images"; exit 1; }

            echo "🛑 Stopping existing services..."
            docker compose -f ${{ env.COMPOSE_FILE }} down

            echo "🚀 Starting staging services..."
            docker compose -f ${{ env.COMPOSE_FILE }} up -d --build || { echo "❌ Failed to start services"; exit 1; }

            echo "⏳ Services started. Waiting 30 seconds for services to stabilize..."
            sleep 30

            echo "🔍 Checking service status..."
            docker compose -f ${{ env.COMPOSE_FILE }} ps

            # Clean up
            echo "🧹 Cleaning up old images..."
            docker image prune -f

            echo "🔐 Logging out of GitHub Container Registry..."
            docker logout ghcr.io

            echo "✅ Staging deployment completed successfully!"
            echo ""
            echo "📊 Deployment Summary:"
            echo "  Environment: staging"
            echo "  Image Tag: ${{ env.IMAGE_TAG }}"
            echo "  Deployed by: ${{ env.DEPLOYED_BY }}"
            echo "  Completed: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"

  deploy-production:
    runs-on: ubuntu-latest
    environment: production
    if: |
      (github.event_name == 'repository_dispatch' && github.event.action == 'deploy-production') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set deployment variables for production
        id: vars
        run: |
          # Handle both repository_dispatch and workflow_dispatch triggers
          if [[ "${{ github.event_name }}" == "repository_dispatch" ]]; then
            IMAGE_TAG="${{ github.event.client_payload.image_tag }}"
            COMMIT_SHA="${{ github.event.client_payload.commit_sha }}"
            BUILD_NUMBER="${{ github.event.client_payload.build_number }}"
            DEPLOYED_BY="${{ github.event.client_payload.deployed_by }}"
            REASON="${{ github.event.client_payload.reason }}"
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            IMAGE_TAG="${{ github.event.inputs.image_tag }}"
            COMMIT_SHA="${{ github.sha }}"
            BUILD_NUMBER="${{ github.run_number }}"
            DEPLOYED_BY="${{ github.actor }}"
            REASON="${{ github.event.inputs.reason }}"
          else
            echo "❌ Unsupported event type: ${{ github.event_name }}"
            exit 1
          fi

          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV
          echo "COMMIT_SHA=${COMMIT_SHA}" >> $GITHUB_ENV
          echo "BUILD_NUMBER=${BUILD_NUMBER}" >> $GITHUB_ENV
          echo "DEPLOYED_BY=${DEPLOYED_BY}" >> $GITHUB_ENV
          echo "REASON=${REASON}" >> $GITHUB_ENV

          # Set production deployment paths and compose files
          echo "DEPLOY_PATH=/opt/yezhome/yezhome-prod" >> $GITHUB_ENV
          echo "COMPOSE_FILE=docker-compose.yml" >> $GITHUB_ENV
          echo "ENV_FILE_LOCAL=.env.prod" >> $GITHUB_ENV
          echo "ENV_FILE_REMOTE=.env" >> $GITHUB_ENV

      - name: Create environment file for production
        run: |
          echo "📝 Creating production environment file..."

          ENV_FILE="${{ env.ENV_FILE_LOCAL }}"
          echo "# Deployment metadata" > ${ENV_FILE}
          echo "IMAGE_TAG=${{ env.IMAGE_TAG }}" >> ${ENV_FILE}
          echo "BUILD_NUMBER=${{ env.BUILD_NUMBER }}" >> ${ENV_FILE}
          echo "COMMIT_SHA=${{ env.COMMIT_SHA }}" >> ${ENV_FILE}
          echo "LAST_DEPLOYED=$(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# Database Configuration - Production" >> ${ENV_FILE}
          echo "DB_HOST=${{ vars.DB_HOST }}" >> ${ENV_FILE}
          echo "POSTGRES_DB=${{ vars.POSTGRES_DB }}" >> ${ENV_FILE}
          echo "POSTGRES_USER=${{ secrets.DB_USER }}" >> ${ENV_FILE}
          echo "POSTGRES_PASSWORD=${{ secrets.DB_PASSWORD }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# JWT Configuration - Production" >> ${ENV_FILE}
          echo "JWT_KEY=${{ secrets.JWT_KEY }}" >> ${ENV_FILE}
          echo "JWT_ISSUER=${{ vars.JWT_ISSUER }}" >> ${ENV_FILE}
          echo "JWT_AUDIENCE=${{ vars.JWT_AUDIENCE }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# AWS Configuration - Production" >> ${ENV_FILE}
          echo "AWS_S3_ACCESS_KEY=${{ secrets.AWS_S3_ACCESS_KEY }}" >> ${ENV_FILE}
          echo "AWS_S3_SECRET_KEY=${{ secrets.AWS_S3_SECRET_KEY }}" >> ${ENV_FILE}
          echo "AWS_S3_REGION=${{ vars.AWS_S3_REGION }}" >> ${ENV_FILE}
          echo "AWS_S3_BUCKET_NAME=${{ vars.AWS_S3_BUCKET_NAME }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# AWS SES Configuration - Production" >> ${ENV_FILE}
          echo "AWS_SES_ACCESS_KEY=${{ secrets.AWS_SES_ACCESS_KEY }}" >> ${ENV_FILE}
          echo "AWS_SES_SECRET_KEY=${{ secrets.AWS_SES_SECRET_KEY }}" >> ${ENV_FILE}
          echo "AWS_SES_REGION=${{ vars.AWS_SES_REGION }}" >> ${ENV_FILE}
          echo "AWS_SES_FROM_EMAIL=${{ vars.AWS_SES_FROM_EMAIL }}" >> ${ENV_FILE}
          echo "AWS_SES_FROM_NAME=${{ vars.AWS_SES_FROM_NAME }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          echo "# Internal API Configuration - Production" >> ${ENV_FILE}
          echo "INTERNAL_JWT_KEY=${{ secrets.INTERNAL_JWT_KEY }}" >> ${ENV_FILE}
          echo "INTERNAL_JWT_ISSUER=${{ vars.INTERNAL_JWT_ISSUER }}" >> ${ENV_FILE}
          echo "INTERNAL_JWT_AUDIENCE=${{ vars.INTERNAL_JWT_AUDIENCE }}" >> ${ENV_FILE}
          echo "" >> ${ENV_FILE}

          # Add deployment metadata
          echo "DEPLOYED_BY=${{ env.DEPLOYED_BY }}" >> ${ENV_FILE}

      - name: Create deployment directory on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            echo "📁 Creating deployment directory: ${{ env.DEPLOY_PATH }}"
            mkdir -p ${{ env.DEPLOY_PATH }}
            echo "✅ Directories created successfully"

      - name: Copy files to Droplet
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          source: "Dockerfile,${{ env.COMPOSE_FILE }},${{ env.ENV_FILE_LOCAL }}"
          target: ${{ env.DEPLOY_PATH }}

      - name: Rename environment file on Droplet
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          script: |
            cd ${{ env.DEPLOY_PATH }}
            mv ${{ env.ENV_FILE_LOCAL }} ${{ env.ENV_FILE_REMOTE }}
            echo "✅ Environment file renamed"

      - name: SSH to Droplet and deploy production
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.DROPLET_IP }}
          username: ${{ secrets.DROPLET_USERNAME }}
          key: ${{ secrets.DROPLET_SSH_KEY }}
          timeout: 600s
          command_timeout: 15m
          script: |
            echo "🚀 Deploying to production environment..."
            echo "📁 Deploy path: ${{ env.DEPLOY_PATH }}"
            echo "🐳 Image tag: ${{ env.IMAGE_TAG }}"
            echo "📄 Compose file: ${{ env.COMPOSE_FILE }}"

            # Navigate to deployment directory
            cd ${{ env.DEPLOY_PATH }} || { echo "❌ Failed to change directory"; exit 1; }

            # Set secure permissions
            chmod 600 ${{ env.ENV_FILE_REMOTE }}

            echo "🔐 Logging into GitHub Container Registry..."
            echo "${{ secrets.PULL_PAT }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            echo "🔄 Pulling latest images..."
            docker compose -f ${{ env.COMPOSE_FILE }} pull || { echo "❌ Failed to pull images"; exit 1; }

            echo "🛑 Stopping existing services..."
            docker compose -f ${{ env.COMPOSE_FILE }} down

            echo "🚀 Starting production services..."
            docker compose -f ${{ env.COMPOSE_FILE }} up -d --build || { echo "❌ Failed to start services"; exit 1; }

            echo "⏳ Services started. Waiting 30 seconds for services to stabilize..."
            sleep 30

            echo "🔍 Checking service status..."
            docker compose -f ${{ env.COMPOSE_FILE }} ps

            # Clean up
            echo "🧹 Cleaning up old images..."
            docker image prune -f

            echo "🔐 Logging out of GitHub Container Registry..."
            docker logout ghcr.io

            echo "✅ Production deployment completed successfully!"
            echo ""
            echo "📊 Deployment Summary:"
            echo "  Environment: production"
            echo "  Image Tag: ${{ env.IMAGE_TAG }}"
            echo "  Deployed by: ${{ env.DEPLOYED_BY }}"
            echo "  Reason: ${{ env.REASON }}"
            echo "  Completed: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"
